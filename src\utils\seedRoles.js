require('dotenv').config();
const mongoose = require('mongoose');
const Role = require('../models/Role');
const User = require('../models/User');
const { ROLES } = require('./constants');
const logger = require('./logger');

/**
 * Seed roles and create admin user
 */
const seedRoles = async () => {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    logger.info('Connected to MongoDB for seeding');

    // Create roles if they don't exist
    const rolesToCreate = [
      { role_name: ROLES.ADMIN },
      { role_name: ROLES.NORMAL_USER }
    ];

    for (const roleData of rolesToCreate) {
      const existingRole = await Role.findOne({ role_name: roleData.role_name });
      
      if (!existingRole) {
        const role = await Role.create(roleData);
        logger.info(`Created role: ${role.role_name}`);
      } else {
        logger.info(`Role already exists: ${roleData.role_name}`);
      }
    }

    // Create admin user if specified in environment
    if (process.env.ADMIN_EMAIL && process.env.ADMIN_PASSWORD) {
      const existingAdmin = await User.findOne({ email: process.env.ADMIN_EMAIL });
      
      if (!existingAdmin) {
        const adminUser = await User.create({
          email: process.env.ADMIN_EMAIL,
          password: process.env.ADMIN_PASSWORD
        });
        
        logger.info(`Created admin user: ${adminUser.email}`);
        logger.info('Note: You can create an account and the admin user will automatically be assigned as admin');
      } else {
        logger.info(`Admin user already exists: ${process.env.ADMIN_EMAIL}`);
      }
    }

    logger.info('Seeding completed successfully');
    process.exit(0);

  } catch (error) {
    logger.error('Seeding failed:', error);
    process.exit(1);
  }
};

// Run seeding if this file is executed directly
if (require.main === module) {
  seedRoles();
}

module.exports = seedRoles;
