const express = require('express');
const {
  createDestination,
  getDestinations,
  getDestinationsByAccount,
  getDestinationById,
  updateDestination,
  deleteDestination
} = require('../controllers/destinationController');
const { protect, requireAccountAccess } = require('../middleware/auth');
const { validate, destinationSchemas } = require('../middleware/validation');
const { generalApiRateLimit } = require('../middleware/rateLimiter');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Destinations
 *   description: Destination management endpoints
 */

// Apply general rate limiting
router.use(generalApiRateLimit);

// All routes require authentication
router.use(protect);

// Routes
router.route('/')
  .get(getDestinations)
  .post(validate(destinationSchemas.create), requireAccountAccess(), createDestination);

router.get('/account/:accountId', requireAccountAccess(), getDestinationsByAccount);

router.route('/:id')
  .get(getDestinationById)
  .put(validate(destinationSchemas.update), updateDestination)
  .delete(deleteDestination);

module.exports = router;
