const {
  ROLES,
  LOG_STATUS,
  HTTP_METHODS,
  QUEUE_NAMES,
  CACHE_KEYS,
  CACHE_TTL,
  RATE_LIMITS,
  VALIDATION
} = require('../../src/utils/constants');

describe('Constants', () => {
  describe('ROLES', () => {
    it('should have correct role definitions', () => {
      expect(ROLES.ADMIN).toBe('Admin');
      expect(ROLES.NORMAL_USER).toBe('Normal user');
    });
  });

  describe('LOG_STATUS', () => {
    it('should have all required status values', () => {
      expect(LOG_STATUS.SUCCESS).toBe('success');
      expect(LOG_STATUS.FAILED).toBe('failed');
      expect(LOG_STATUS.PENDING).toBe('pending');
      expect(LOG_STATUS.PROCESSING).toBe('processing');
    });
  });

  describe('HTTP_METHODS', () => {
    it('should have all supported HTTP methods', () => {
      expect(HTTP_METHODS.GET).toBe('GET');
      expect(HTTP_METHODS.POST).toBe('POST');
      expect(HTTP_METHODS.PUT).toBe('PUT');
      expect(HTTP_METHODS.PATCH).toBe('PATCH');
      expect(HTTP_METHODS.DELETE).toBe('DELETE');
    });
  });

  describe('QUEUE_NAMES', () => {
    it('should have webhook processor queue name', () => {
      expect(QUEUE_NAMES.WEBHOOK_PROCESSOR).toBe('webhook-processor');
    });
  });

  describe('CACHE_KEYS', () => {
    it('should have all cache key prefixes', () => {
      expect(CACHE_KEYS.ACCOUNT_PREFIX).toBe('account:');
      expect(CACHE_KEYS.USER_PREFIX).toBe('user:');
      expect(CACHE_KEYS.DESTINATION_PREFIX).toBe('destination:');
      expect(CACHE_KEYS.LOG_PREFIX).toBe('log:');
    });
  });

  describe('CACHE_TTL', () => {
    it('should have TTL values in seconds', () => {
      expect(CACHE_TTL.SHORT).toBe(300);
      expect(CACHE_TTL.MEDIUM).toBe(1800);
      expect(CACHE_TTL.LONG).toBe(3600);
    });
  });

  describe('RATE_LIMITS', () => {
    it('should have data handler rate limits', () => {
      expect(RATE_LIMITS.DATA_HANDLER.WINDOW_MS).toBe(1000);
      expect(RATE_LIMITS.DATA_HANDLER.MAX_REQUESTS).toBe(5);
    });

    it('should have general API rate limits', () => {
      expect(RATE_LIMITS.GENERAL_API.WINDOW_MS).toBe(900000); // 15 minutes
      expect(RATE_LIMITS.GENERAL_API.MAX_REQUESTS).toBe(100);
    });
  });

  describe('VALIDATION', () => {
    it('should have validation constants', () => {
      expect(VALIDATION.PASSWORD_MIN_LENGTH).toBe(6);
      expect(VALIDATION.ACCOUNT_NAME_MIN_LENGTH).toBe(2);
      expect(VALIDATION.ACCOUNT_NAME_MAX_LENGTH).toBe(100);
      expect(VALIDATION.URL_MAX_LENGTH).toBe(2048);
    });
  });
});
