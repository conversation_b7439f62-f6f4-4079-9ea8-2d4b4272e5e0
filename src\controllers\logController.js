const Log = require('../models/Log');
const AccountMember = require('../models/AccountMember');
const webhookService = require('../services/webhookService');
const { asyncHandler } = require('../middleware/errorHandler');
const { createPaginationMeta } = require('../utils/helpers');
const { LOG_STATUS } = require('../utils/constants');
const logger = require('../utils/logger');

/**
 * @swagger
 * /api/logs:
 *   get:
 *     summary: Get logs (filtered by user's account access)
 *     tags: [Logs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: account_id
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [success, failed, pending, processing]
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Logs retrieved successfully
 */
const getLogs = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const accountId = req.query.account_id;
  const status = req.query.status;
  const startDate = req.query.start_date;
  const endDate = req.query.end_date;
  const skip = (page - 1) * limit;

  // Get user's accessible accounts
  const memberships = await AccountMember.find({ user_id: req.user._id });
  const accountIds = memberships.map(m => m.account_id);

  let query = {
    account_id: { $in: accountIds }
  };

  // Filter by specific account if provided
  if (accountId && accountIds.includes(accountId)) {
    query.account_id = accountId;
  }

  // Filter by status
  if (status && Object.values(LOG_STATUS).includes(status)) {
    query.status = status;
  }

  // Filter by date range
  if (startDate || endDate) {
    query.received_timestamp = {};
    if (startDate) query.received_timestamp.$gte = new Date(startDate);
    if (endDate) query.received_timestamp.$lte = new Date(endDate);
  }

  // Get logs with pagination
  const [logs, total] = await Promise.all([
    Log.find(query)
      .populate('account_id', 'account_name account_id')
      .populate('destination_id', 'url http_method')
      .sort({ received_timestamp: -1 })
      .skip(skip)
      .limit(limit),
    Log.countDocuments(query)
  ]);

  const pagination = createPaginationMeta(page, limit, total);

  res.status(200).json({
    success: true,
    message: 'Logs retrieved successfully',
    data: { logs, pagination }
  });
});

/**
 * @swagger
 * /api/logs/account/{accountId}:
 *   get:
 *     summary: Get logs by account ID
 *     tags: [Logs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: accountId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Account logs retrieved successfully
 */
const getLogsByAccount = asyncHandler(async (req, res) => {
  const accountId = req.params.accountId;
  const status = req.query.status;
  const startDate = req.query.start_date;
  const endDate = req.query.end_date;

  const options = {};
  if (status) options.status = status;
  if (startDate) options.startDate = startDate;
  if (endDate) options.endDate = endDate;

  const logs = await Log.findByAccountId(accountId, options)
    .populate('destination_id', 'url http_method')
    .limit(100); // Limit to prevent large responses

  res.status(200).json({
    success: true,
    message: 'Account logs retrieved successfully',
    data: { logs }
  });
});

/**
 * @swagger
 * /api/logs/{id}:
 *   get:
 *     summary: Get log by ID
 *     tags: [Logs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Log retrieved successfully
 */
const getLogById = asyncHandler(async (req, res) => {
  const logId = req.params.id;

  const log = await Log.findById(logId)
    .populate('account_id', 'account_name account_id')
    .populate('destination_id', 'url http_method headers');

  if (!log) {
    return res.status(404).json({
      success: false,
      message: 'Log not found'
    });
  }

  res.status(200).json({
    success: true,
    message: 'Log retrieved successfully',
    data: { log }
  });
});

/**
 * @swagger
 * /api/logs/event/{eventId}:
 *   get:
 *     summary: Get log by event ID
 *     tags: [Logs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: eventId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Log retrieved successfully
 */
const getLogByEventId = asyncHandler(async (req, res) => {
  const eventId = req.params.eventId;

  const logs = await Log.find({ event_id: eventId })
    .populate('account_id', 'account_name account_id')
    .populate('destination_id', 'url http_method headers')
    .sort({ received_timestamp: -1 });

  if (!logs || logs.length === 0) {
    return res.status(404).json({
      success: false,
      message: 'No logs found for this event ID'
    });
  }

  res.status(200).json({
    success: true,
    message: 'Logs retrieved successfully',
    data: { logs }
  });
});

/**
 * @swagger
 * /api/logs/{id}/retry:
 *   post:
 *     summary: Retry failed webhook (admin only)
 *     tags: [Logs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Webhook retry initiated successfully
 */
const retryWebhook = asyncHandler(async (req, res) => {
  const logId = req.params.id;

  const result = await webhookService.retryWebhook(logId);

  if (result.success) {
    logger.info('Webhook retry initiated', {
      logId,
      retriedBy: req.user.email
    });

    res.status(200).json({
      success: true,
      message: 'Webhook retry initiated successfully',
      data: result
    });
  } else {
    res.status(400).json({
      success: false,
      message: result.error || 'Failed to retry webhook'
    });
  }
});

/**
 * @swagger
 * /api/logs/stats/{accountId}:
 *   get:
 *     summary: Get webhook statistics for an account
 *     tags: [Logs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: accountId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Webhook statistics retrieved successfully
 */
const getWebhookStats = asyncHandler(async (req, res) => {
  const accountId = req.params.accountId;
  const startDate = req.query.start_date;
  const endDate = req.query.end_date;

  const options = {};
  if (startDate) options.startDate = startDate;
  if (endDate) options.endDate = endDate;

  const stats = await webhookService.getWebhookStats(accountId, options);

  if (!stats) {
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve webhook statistics'
    });
  }

  res.status(200).json({
    success: true,
    message: 'Webhook statistics retrieved successfully',
    data: { stats }
  });
});

module.exports = {
  getLogs,
  getLogsByAccount,
  getLogById,
  getLogByEventId,
  retryWebhook,
  getWebhookStats
};
