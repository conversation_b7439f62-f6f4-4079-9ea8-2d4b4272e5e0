{"name": "data-pusher", "version": "1.0.0", "description": "Node.js Express web application that receives JSON data and forwards it to various destinations using webhook URLs", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "seed": "node src/utils/seedRoles.js"}, "keywords": ["nodejs", "express", "webhook", "data-pusher", "api"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "bull": "^4.12.2", "redis": "^4.6.11", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "winston": "^3.11.0", "axios": "^1.6.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "crypto": "^1.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8", "mongodb-memory-server": "^9.1.3"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/config/**", "!src/utils/seedRoles.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}