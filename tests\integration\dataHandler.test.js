const request = require('supertest');
const app = require('../../src/app');
const { setupTestDB, teardownTestDB, clearTestDB } = require('../setup');
const Role = require('../../src/models/Role');
const User = require('../../src/models/User');
const Account = require('../../src/models/Account');
const Destination = require('../../src/models/Destination');
const AccountMember = require('../../src/models/AccountMember');
const { ROLES } = require('../../src/utils/constants');

describe('Data Handler Endpoints', () => {
  let adminUser, account, destination, authToken;

  beforeAll(async () => {
    await setupTestDB();
    
    // Create roles
    const adminRole = await Role.create({ role_name: ROLES.ADMIN });
    await Role.create({ role_name: ROLES.NORMAL_USER });

    // Create admin user
    adminUser = await User.create({
      email: '<EMAIL>',
      password: 'password123'
    });

    // Get auth token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });
    
    authToken = loginResponse.body.data.token;

    // Create account
    account = await Account.create({
      account_name: 'Test Account',
      created_by: adminUser._id,
      updated_by: adminUser._id
    });

    // Add admin as account member
    await AccountMember.create({
      account_id: account._id,
      user_id: adminUser._id,
      role_id: adminRole._id,
      created_by: adminUser._id,
      updated_by: adminUser._id
    });

    // Create destination
    destination = await Destination.create({
      account_id: account._id,
      url: 'https://httpbin.org/post',
      http_method: 'POST',
      headers: new Map([
        ['Content-Type', 'application/json'],
        ['Authorization', 'Bearer test-token']
      ]),
      created_by: adminUser._id,
      updated_by: adminUser._id
    });
  });

  afterAll(async () => {
    await teardownTestDB();
  });

  beforeEach(async () => {
    // Clear logs between tests but keep other data
    const Log = require('../../src/models/Log');
    await Log.deleteMany({});
  });

  describe('POST /server/incoming_data', () => {
    it('should process data successfully with valid token', async () => {
      const testData = {
        user_id: 123,
        action: 'user.update',
        data: {
          name: 'John Doe',
          email: '<EMAIL>'
        }
      };

      const response = await request(app)
        .post('/server/incoming_data')
        .set('CL-X-TOKEN', account.app_secret_token)
        .set('CL-X-EVENT-ID', 'test-event-123')
        .send(testData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Data Received');
      expect(response.body.data.event_id).toBe('test-event-123');
      expect(response.body.data.destinations_count).toBe(1);
    });

    it('should auto-generate event ID if not provided', async () => {
      const testData = {
        user_id: 456,
        action: 'user.create'
      };

      const response = await request(app)
        .post('/server/incoming_data')
        .set('CL-X-TOKEN', account.app_secret_token)
        .send(testData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.event_id).toBeDefined();
      expect(response.body.data.event_id).not.toBe('test-event-123');
    });

    it('should return error for missing token', async () => {
      const testData = { test: 'data' };

      const response = await request(app)
        .post('/server/incoming_data')
        .send(testData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('CL-X-TOKEN header is required');
    });

    it('should return error for invalid token', async () => {
      const testData = { test: 'data' };

      const response = await request(app)
        .post('/server/incoming_data')
        .set('CL-X-TOKEN', 'invalid-token')
        .send(testData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Invalid secret token');
    });

    it('should return error for empty data', async () => {
      const response = await request(app)
        .post('/server/incoming_data')
        .set('CL-X-TOKEN', account.app_secret_token)
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Invalid Data');
    });

    it('should handle account with no destinations', async () => {
      // Create account without destinations
      const accountWithoutDestinations = await Account.create({
        account_name: 'Account Without Destinations',
        created_by: adminUser._id,
        updated_by: adminUser._id
      });

      const testData = { test: 'data' };

      const response = await request(app)
        .post('/server/incoming_data')
        .set('CL-X-TOKEN', accountWithoutDestinations.app_secret_token)
        .send(testData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.destinations_count).toBe(0);
      expect(response.body.data.note).toBe('No destinations configured for this account');
    });
  });

  describe('GET /server/health', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/server/health')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Data handler service is healthy');
      expect(response.body.data.timestamp).toBeDefined();
    });
  });
});
