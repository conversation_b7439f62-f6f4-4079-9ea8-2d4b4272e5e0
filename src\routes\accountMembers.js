const express = require('express');
const {
  addAccountMember,
  getAccountMembers,
  getUserMemberships,
  getAllAccountMembers,
  updateAccountMember,
  removeAccountMember
} = require('../controllers/accountMemberController');
const { protect, requireAdmin, requireAccountAdmin } = require('../middleware/auth');
const { validate, accountMemberSchemas } = require('../middleware/validation');
const { generalApiRateLimit } = require('../middleware/rateLimiter');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Account Members
 *   description: Account membership management endpoints
 */

// Apply general rate limiting
router.use(generalApiRateLimit);

// All routes require authentication
router.use(protect);

// Routes
router.route('/')
  .get(requireAdmin, getAllAccountMembers)
  .post(requireAdmin, validate(accountMemberSchemas.create), addAccountMember);

router.get('/account/:accountId', getAccountMembers);
router.get('/user/:userId', getUserMemberships);

router.route('/:id')
  .put(requireAdmin, validate(accountMemberSchemas.update), updateAccountMember)
  .delete(requireAdmin, removeAccountMember);

module.exports = router;
