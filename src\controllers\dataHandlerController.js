const Account = require('../models/Account');
const Destination = require('../models/Destination');
const queueService = require('../services/queueService');
const webhookService = require('../services/webhookService');
const cacheService = require('../services/cacheService');
const { asyncHandler } = require('../middleware/errorHandler');
const { generateEventId } = require('../utils/helpers');
const logger = require('../utils/logger');

/**
 * Middleware to authenticate account using secret token
 */
const authenticateAccount = asyncHandler(async (req, res, next) => {
  const secretToken = req.headers['cl-x-token'];
  
  if (!secretToken) {
    return res.status(401).json({
      success: false,
      message: 'CL-X-TOKEN header is required'
    });
  }

  // Try to get account from cache first
  let account = await cacheService.getCachedAccount(`token:${secretToken}`);
  
  if (!account) {
    // Get account by secret token
    account = await Account.findBySecretToken(secretToken);
    
    if (!account) {
      return res.status(401).json({
        success: false,
        message: 'Invalid secret token'
      });
    }

    // Cache account with token key
    await cacheService.set(`token:${secretToken}`, account.toJSON(), 3600); // 1 hour
  }

  req.account = account;
  next();
});

/**
 * @swagger
 * /server/incoming_data:
 *   post:
 *     summary: Receive JSON data and forward to destinations
 *     tags: [Data Handler]
 *     parameters:
 *       - in: header
 *         name: CL-X-TOKEN
 *         required: true
 *         schema:
 *           type: string
 *         description: Account secret token
 *       - in: header
 *         name: CL-X-EVENT-ID
 *         schema:
 *           type: string
 *         description: Optional event ID (auto-generated if not provided)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             description: Any JSON data structure
 *     responses:
 *       200:
 *         description: Data received and queued for processing
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Data Received"
 *                 data:
 *                   type: object
 *                   properties:
 *                     event_id:
 *                       type: string
 *                     destinations_count:
 *                       type: number
 *       400:
 *         description: Invalid data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid Data"
 *       401:
 *         description: Unauthorized
 *       429:
 *         description: Rate limit exceeded
 */
const handleIncomingData = asyncHandler(async (req, res) => {
  const receivedData = req.body;
  let eventId = req.headers['cl-x-event-id'];

  // Validate that we have data
  if (!receivedData || Object.keys(receivedData).length === 0) {
    return res.status(400).json({
      success: false,
      message: 'Invalid Data'
    });
  }

  // Generate event ID if not provided
  if (!eventId) {
    eventId = generateEventId();
  }

  try {
    // Get destinations for this account
    let destinations = await cacheService.getCachedDestinations(req.account._id.toString());
    
    if (!destinations) {
      destinations = await Destination.findByAccountId(req.account._id);
      
      // Cache destinations
      await cacheService.cacheDestinations(req.account._id.toString(), destinations);
    }

    if (!destinations || destinations.length === 0) {
      logger.warn('No destinations found for account', {
        accountId: req.account._id,
        accountName: req.account.account_name,
        eventId
      });

      return res.status(200).json({
        success: true,
        message: 'Data Received',
        data: {
          event_id: eventId,
          destinations_count: 0,
          note: 'No destinations configured for this account'
        }
      });
    }

    // Process each destination
    const processingPromises = destinations.map(async (destination) => {
      try {
        // Add to queue for async processing
        const job = await queueService.addWebhookJob({
          eventId,
          accountId: req.account._id,
          destinationId: destination._id,
          data: receivedData
        });

        if (!job) {
          // If queue is not available, process synchronously
          logger.warn('Queue not available, processing webhook synchronously', {
            eventId,
            destinationId: destination._id
          });

          await webhookService.sendWebhook(
            destination,
            receivedData,
            eventId,
            req.account._id
          );
        }

        return { destinationId: destination._id, status: 'queued' };
      } catch (error) {
        logger.error('Failed to process destination', {
          eventId,
          destinationId: destination._id,
          error: error.message
        });
        return { destinationId: destination._id, status: 'failed', error: error.message };
      }
    });

    await Promise.all(processingPromises);

    logger.info('Data received and queued for processing', {
      accountId: req.account._id,
      accountName: req.account.account_name,
      eventId,
      destinationsCount: destinations.length,
      dataSize: JSON.stringify(receivedData).length
    });

    res.status(200).json({
      success: true,
      message: 'Data Received',
      data: {
        event_id: eventId,
        destinations_count: destinations.length
      }
    });

  } catch (error) {
    logger.error('Error processing incoming data', {
      accountId: req.account._id,
      eventId,
      error: error.message,
      stack: error.stack
    });

    res.status(500).json({
      success: false,
      message: 'Internal server error while processing data'
    });
  }
});

/**
 * @swagger
 * /server/health:
 *   get:
 *     summary: Health check for data handler service
 *     tags: [Data Handler]
 *     responses:
 *       200:
 *         description: Service is healthy
 */
const healthCheck = asyncHandler(async (req, res) => {
  const queueStats = await queueService.getWebhookQueueStats();
  
  res.status(200).json({
    success: true,
    message: 'Data handler service is healthy',
    data: {
      timestamp: new Date().toISOString(),
      queue_stats: queueStats
    }
  });
});

module.exports = {
  authenticateAccount,
  handleIncomingData,
  healthCheck
};
