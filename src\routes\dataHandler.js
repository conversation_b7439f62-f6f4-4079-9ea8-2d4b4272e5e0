const express = require('express');
const {
  authenticateAccount,
  handleIncomingData,
  healthCheck
} = require('../controllers/dataHandlerController');
const { validate, dataHandlerSchema } = require('../middleware/validation');
const { dataHandlerRateLimit } = require('../middleware/rateLimiter');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Data Handler
 *   description: Core data processing endpoints
 */

// Health check (no authentication required)
router.get('/health', healthCheck);

// Data handler endpoint with account authentication and rate limiting
router.post('/incoming_data', 
  authenticateAccount,
  dataHandlerRateLimit,
  validate(dataHandlerSchema),
  handleIncomingData
);

module.exports = router;
