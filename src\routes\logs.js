const express = require('express');
const {
  getLogs,
  getLogsByAccount,
  getLogById,
  getLogByEventId,
  retryWebhook,
  getWebhookStats
} = require('../controllers/logController');
const { protect, requireAdmin, requireAccountAccess } = require('../middleware/auth');
const { generalApiRateLimit } = require('../middleware/rateLimiter');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Logs
 *   description: Log management and webhook monitoring endpoints
 */

// Apply general rate limiting
router.use(generalApiRateLimit);

// All routes require authentication
router.use(protect);

// Routes
router.get('/', getLogs);
router.get('/account/:accountId', requireAccountAccess(), getLogsByAccount);
router.get('/event/:eventId', getLogByEventId);
router.get('/stats/:accountId', requireAccountAccess(), getWebhookStats);

router.get('/:id', getLogById);
router.post('/:id/retry', requireAdmin, retryWebhook);

module.exports = router;
