{"cause": {"commonWireVersion": 0, "compatible": true, "heartbeatFrequencyMS": 10000, "localThresholdMS": 15, "logicalSessionTimeoutMinutes": null, "maxElectionId": null, "maxSetVersion": null, "servers": {"localhost:27017": {"$clusterTime": null, "address": "localhost:27017", "arbiters": [], "electionId": null, "error": {"beforeHandshake": false, "errorLabelSet": {}}, "hosts": [], "iscryptd": false, "lastUpdateTime": 412482281, "lastWriteDate": 0, "logicalSessionTimeoutMinutes": null, "maxBsonObjectSize": null, "maxMessageSizeBytes": null, "maxWireVersion": 0, "maxWriteBatchSize": null, "me": null, "minRoundTripTime": 0, "minWireVersion": 0, "passives": [], "primary": null, "roundTripTime": -1, "setName": null, "setVersion": null, "tags": {}, "topologyVersion": null, "type": "Unknown"}}, "setName": null, "stale": false, "type": "Unknown"}, "errorLabelSet": {}, "level": "error", "message": "Seeding failed: connect ECONNREFUSED ::1:27017", "reason": {"commonWireVersion": 0, "compatible": true, "heartbeatFrequencyMS": 10000, "localThresholdMS": 15, "logicalSessionTimeoutMinutes": null, "maxElectionId": null, "maxSetVersion": null, "servers": {"localhost:27017": {"$clusterTime": null, "address": "localhost:27017", "arbiters": [], "electionId": null, "error": {"beforeHandshake": false, "errorLabelSet": {}}, "hosts": [], "iscryptd": false, "lastUpdateTime": 412482281, "lastWriteDate": 0, "logicalSessionTimeoutMinutes": null, "maxBsonObjectSize": null, "maxMessageSizeBytes": null, "maxWireVersion": 0, "maxWriteBatchSize": null, "me": null, "minRoundTripTime": 0, "minWireVersion": 0, "passives": [], "primary": null, "roundTripTime": -1, "setName": null, "setVersion": null, "tags": {}, "topologyVersion": null, "type": "Unknown"}}, "setName": null, "stale": false, "type": "Unknown"}, "service": "data-pusher", "stack": "MongooseServerSelectionError: connect ECONNREFUSED ::1:27017\n    at _handleConnectionErrors (C:\\Users\\<USER>\\Desktop\\New folder (2)\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (C:\\Users\\<USER>\\Desktop\\New folder (2)\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async seedRoles (C:\\Users\\<USER>\\Desktop\\New folder (2)\\src\\utils\\seedRoles.js:14:5)", "timestamp": "2025-06-11 17:26:33"}