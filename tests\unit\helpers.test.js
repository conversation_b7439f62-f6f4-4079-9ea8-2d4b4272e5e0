const {
  generateAppSecretToken,
  generateEventId,
  generateRandomString,
  sanitizeForLogging,
  createPaginationMeta,
  isValidEmail,
  isValidUrl,
  deepClone,
} = require("../../src/utils/helpers");

describe("Helper Functions", () => {
  describe("generateAppSecretToken", () => {
    it("should generate a 64-character hex string", () => {
      const token = generateAppSecretToken();
      expect(token).toHaveLength(64);
      expect(token).toMatch(/^[a-f0-9]+$/);
    });

    it("should generate unique tokens", () => {
      const token1 = generateAppSecretToken();
      const token2 = generateAppSecretToken();
      expect(token1).not.toBe(token2);
    });
  });

  describe("generateEventId", () => {
    it("should generate a valid UUID", () => {
      const eventId = generateEventId();
      expect(eventId).toMatch(
        /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
      );
    });

    it("should generate unique event IDs", () => {
      const id1 = generateEventId();
      const id2 = generateEventId();
      expect(id1).not.toBe(id2);
    });
  });

  describe("generateRandomString", () => {
    it("should generate string of specified length", () => {
      const str8 = generateRandomString(8);
      const str16 = generateRandomString(16);
      const str32 = generateRandomString(32);

      expect(str8).toHaveLength(8);
      expect(str16).toHaveLength(16);
      expect(str32).toHaveLength(32);
    });

    it("should generate hex strings", () => {
      const str = generateRandomString(16);
      expect(str).toMatch(/^[a-f0-9]+$/);
    });

    it("should use default length of 16", () => {
      const str = generateRandomString();
      expect(str).toHaveLength(16);
    });
  });

  describe("sanitizeForLogging", () => {
    it("should redact sensitive fields", () => {
      const obj = {
        username: "john",
        password: "secret123",
        token: "abc123",
        authorization: "Bearer xyz",
        data: {
          secret: "hidden",
          public: "visible",
        },
      };

      const sanitized = sanitizeForLogging(obj);

      expect(sanitized.username).toBe("john");
      expect(sanitized.password).toBe("[REDACTED]");
      expect(sanitized.token).toBe("[REDACTED]");
      expect(sanitized.authorization).toBe("[REDACTED]");
      expect(sanitized.data.secret).toBe("[REDACTED]");
      expect(sanitized.data.public).toBe("visible");
    });

    it("should handle null and undefined values", () => {
      const obj = {
        value: null,
        undefined: undefined,
        password: "secret",
      };

      const sanitized = sanitizeForLogging(obj);

      expect(sanitized.value).toBeNull();
      expect(sanitized.undefined).toBeUndefined();
      expect(sanitized.password).toBe("[REDACTED]");
    });
  });

  describe("createPaginationMeta", () => {
    it("should create correct pagination metadata", () => {
      const meta = createPaginationMeta(2, 10, 25);

      expect(meta).toEqual({
        currentPage: 2,
        totalPages: 3,
        totalItems: 25,
        itemsPerPage: 10,
        hasNextPage: true,
        hasPrevPage: true,
      });
    });

    it("should handle first page correctly", () => {
      const meta = createPaginationMeta(1, 10, 25);

      expect(meta.hasPrevPage).toBe(false);
      expect(meta.hasNextPage).toBe(true);
    });

    it("should handle last page correctly", () => {
      const meta = createPaginationMeta(3, 10, 25);

      expect(meta.hasPrevPage).toBe(true);
      expect(meta.hasNextPage).toBe(false);
    });

    it("should handle single page correctly", () => {
      const meta = createPaginationMeta(1, 10, 5);

      expect(meta.totalPages).toBe(1);
      expect(meta.hasPrevPage).toBe(false);
      expect(meta.hasNextPage).toBe(false);
    });
  });

  describe("isValidEmail", () => {
    it("should validate correct email addresses", () => {
      const validEmails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ];

      validEmails.forEach((email) => {
        expect(isValidEmail(email)).toBe(true);
      });
    });

    it("should reject invalid email addresses", () => {
      const invalidEmails = [
        "invalid-email",
        "@example.com",
        "user@",
        "user@.com",
        "",
      ];

      invalidEmails.forEach((email) => {
        expect(isValidEmail(email)).toBe(false);
      });
    });
  });

  describe("isValidUrl", () => {
    it("should validate correct URLs", () => {
      const validUrls = [
        "https://example.com",
        "http://test.org/path",
        "https://api.example.com/v1/webhook",
        "http://localhost:3000/callback",
      ];

      validUrls.forEach((url) => {
        expect(isValidUrl(url)).toBe(true);
      });
    });

    it("should reject invalid URLs", () => {
      const invalidUrls = [
        "not-a-url",
        "example.com",
        "",
        "http://",
        "https://",
      ];

      invalidUrls.forEach((url) => {
        expect(isValidUrl(url)).toBe(false);
      });
    });
  });

  describe("deepClone", () => {
    it("should create deep copy of object", () => {
      const original = {
        name: "test",
        nested: {
          value: 123,
          array: [1, 2, 3],
        },
      };

      const cloned = deepClone(original);

      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
      expect(cloned.nested).not.toBe(original.nested);
      expect(cloned.nested.array).not.toBe(original.nested.array);
    });

    it("should handle arrays", () => {
      const original = [1, { a: 2 }, [3, 4]];
      const cloned = deepClone(original);

      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
      expect(cloned[1]).not.toBe(original[1]);
      expect(cloned[2]).not.toBe(original[2]);
    });

    it("should handle primitive values", () => {
      expect(deepClone("string")).toBe("string");
      expect(deepClone(123)).toBe(123);
      expect(deepClone(true)).toBe(true);
      expect(deepClone(null)).toBeNull();
    });
  });
});
