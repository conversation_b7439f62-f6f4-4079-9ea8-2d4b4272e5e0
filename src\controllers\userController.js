const User = require('../models/User');
const authService = require('../services/authService');
const cacheService = require('../services/cacheService');
const { asyncHandler } = require('../middleware/errorHandler');
const { createPaginationMeta } = require('../utils/helpers');
const logger = require('../utils/logger');

/**
 * @swagger
 * /api/users:
 *   get:
 *     summary: Get all users (admin only)
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 */
const getUsers = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const search = req.query.search || '';
  const skip = (page - 1) * limit;

  let query = {};

  // Add search filter
  if (search) {
    query.email = { $regex: search, $options: 'i' };
  }

  // Get users with pagination
  const [users, total] = await Promise.all([
    User.find(query)
      .populate('created_by', 'email')
      .populate('updated_by', 'email')
      .sort({ created_at: -1 })
      .skip(skip)
      .limit(limit),
    User.countDocuments(query)
  ]);

  const pagination = createPaginationMeta(page, limit, total);

  res.status(200).json({
    success: true,
    message: 'Users retrieved successfully',
    data: { users, pagination }
  });
});

/**
 * @swagger
 * /api/users/{id}:
 *   get:
 *     summary: Get user by ID
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User retrieved successfully
 */
const getUserById = asyncHandler(async (req, res) => {
  const userId = req.params.id;

  // Try cache first
  let user = await cacheService.getCachedUser(userId);

  if (!user) {
    // Get from database
    user = await User.findById(userId)
      .populate('created_by', 'email')
      .populate('updated_by', 'email');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Cache the result
    await cacheService.cacheUser(userId, user.toJSON());
  }

  res.status(200).json({
    success: true,
    message: 'User retrieved successfully',
    data: { user }
  });
});

/**
 * @swagger
 * /api/users:
 *   post:
 *     summary: Create a new user (admin only)
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 minLength: 6
 *     responses:
 *       201:
 *         description: User created successfully
 */
const createUser = asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  // Create user
  const user = await authService.register({ email, password }, req.user);

  logger.info('User created by admin', {
    userId: user._id,
    email: user.email,
    createdBy: req.user.email
  });

  res.status(201).json({
    success: true,
    message: 'User created successfully',
    data: { user }
  });
});

/**
 * @swagger
 * /api/users/{id}:
 *   put:
 *     summary: Update user
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 minLength: 6
 *     responses:
 *       200:
 *         description: User updated successfully
 */
const updateUser = asyncHandler(async (req, res) => {
  const userId = req.params.id;
  const updateData = req.body;

  // Update user
  const user = await authService.updateUser(userId, updateData, req.user);

  // Invalidate cache
  await cacheService.invalidateUserCache(userId);

  res.status(200).json({
    success: true,
    message: 'User updated successfully',
    data: { user }
  });
});

/**
 * @swagger
 * /api/users/{id}:
 *   delete:
 *     summary: Delete user (admin only)
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User deleted successfully
 */
const deleteUser = asyncHandler(async (req, res) => {
  const userId = req.params.id;

  // Prevent self-deletion
  if (userId === req.user._id.toString()) {
    return res.status(400).json({
      success: false,
      message: 'Cannot delete your own account'
    });
  }

  // Delete user
  await authService.deleteUser(userId, req.user);

  // Invalidate cache
  await cacheService.invalidateUserCache(userId);

  res.status(200).json({
    success: true,
    message: 'User deleted successfully'
  });
});

module.exports = {
  getUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser
};
