{"name": "bull", "version": "4.16.5", "description": "Job manager", "engines": {"node": ">=12"}, "main": "./index.js", "types": "./index.d.ts", "repository": {"type": "git", "url": "git://github.com/OptimalBits/bull.git"}, "keywords": ["job", "queue", "task", "parallel"], "author": "OptimalBits", "license": "MIT", "readmeFilename": "README.md", "dependencies": {"cron-parser": "^4.9.0", "get-port": "^5.1.1", "ioredis": "^5.3.2", "lodash": "^4.17.21", "msgpackr": "^1.11.2", "semver": "^7.5.2", "uuid": "^8.3.0"}, "devDependencies": {"@commitlint/cli": "^7.6.1", "@commitlint/config-conventional": "^7.6.0", "@semantic-release/changelog": "^5.0.1", "@semantic-release/commit-analyzer": "^8.0.1", "@semantic-release/git": "^9.0.0", "@semantic-release/github": "^7.2.1", "@semantic-release/npm": "^7.1.1", "@semantic-release/release-notes-generator": "^9.0.2", "chai": "^4.2.0", "coveralls": "^3.1.0", "delay": "^4.3.0", "eslint": "^7.4.0", "eslint-plugin-mocha": "^7.0.1", "eslint-plugin-node": "^8.0.1", "expect.js": "^0.3.1", "fast-glob": "^3.3.2", "husky": "^4.2.5", "istanbul": "^0.4.5", "lint-staged": "^8.2.1", "minimatch": "^7.4.4", "mocha": "^8.1.1", "mocha-lcov-reporter": "^1.3.0", "moment": "^2.24.0", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "p-reflect": "^1.0.0", "prettier": "^1.19.1", "rimraf": "^3.0.2", "semantic-release": "^17.4.2", "sinon": "^7.5.0"}, "scripts": {"clean:scripts": "rimraf rawScripts lib/scripts", "dc:up": "docker-compose -f docker-compose.yml up -d", "dc:down": "docker-compose -f docker-compose.yml down", "dry-run": "npm publish --dry-run", "generate:raw:scripts": "node generateRawScripts.js", "pretest": "npm-run-all clean:scripts generate:raw:scripts transform:commands lint", "lint": "eslint lib test *.js", "test": "NODE_ENV=test nyc mocha -- 'test/test_*' --recursive --exit", "test:nolint": "NODE_ENV=test mocha 'test/test_*' --recursive --exit", "coverage": "nyc report --reporter=text-lcov | coveralls", "postpublish": "git push && git push --tags", "prettier": "prettier --config package.json --write '**/*.js'", "precommit": "lint-staged", "build": "tsc", "transform:commands": "node ./commandTransform.js ./rawScripts ./lib/scripts"}, "lint-staged": {"*.{js,json}": ["prettier --write", "git add"]}, "prettier": {"singleQuote": true}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "release": {"branches": ["develop"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["@semantic-release/changelog", {"changelogFile": "CHANGELOG.md"}], ["@semantic-release/npm", {"npmPublish": true}], "@semantic-release/github", ["@semantic-release/git", {"assets": ["package.json", "yarn.lock", "CHANGELOG.md"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}]]}}