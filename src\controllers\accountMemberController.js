const AccountMember = require('../models/AccountMember');
const Account = require('../models/Account');
const User = require('../models/User');
const Role = require('../models/Role');
const { asyncHandler } = require('../middleware/errorHandler');
const { createPaginationMeta } = require('../utils/helpers');
const logger = require('../utils/logger');

/**
 * @swagger
 * /api/account-members:
 *   post:
 *     summary: Add user to account (admin only)
 *     tags: [Account Members]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - account_id
 *               - user_id
 *               - role_id
 *             properties:
 *               account_id:
 *                 type: string
 *               user_id:
 *                 type: string
 *               role_id:
 *                 type: string
 *     responses:
 *       201:
 *         description: User added to account successfully
 */
const addAccountMember = asyncHandler(async (req, res) => {
  const { account_id, user_id, role_id } = req.body;

  // Verify account exists
  const account = await Account.findById(account_id);
  if (!account) {
    return res.status(404).json({
      success: false,
      message: 'Account not found'
    });
  }

  // Verify user exists
  const user = await User.findById(user_id);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  // Verify role exists
  const role = await Role.findById(role_id);
  if (!role) {
    return res.status(404).json({
      success: false,
      message: 'Role not found'
    });
  }

  // Check if membership already exists
  const existingMembership = await AccountMember.findOne({
    account_id,
    user_id
  });

  if (existingMembership) {
    return res.status(400).json({
      success: false,
      message: 'User is already a member of this account'
    });
  }

  // Create account member
  const accountMember = await AccountMember.create({
    account_id,
    user_id,
    role_id,
    created_by: req.user._id,
    updated_by: req.user._id
  });

  // Populate the created member
  await accountMember.populate([
    { path: 'user_id', select: 'email' },
    { path: 'role_id', select: 'role_name' },
    { path: 'account_id', select: 'account_name account_id' }
  ]);

  logger.info('User added to account successfully', {
    accountMemberId: accountMember._id,
    accountId: account_id,
    userId: user_id,
    roleId: role_id,
    addedBy: req.user.email
  });

  res.status(201).json({
    success: true,
    message: 'User added to account successfully',
    data: { accountMember }
  });
});

/**
 * @swagger
 * /api/account-members/account/{accountId}:
 *   get:
 *     summary: Get members of an account
 *     tags: [Account Members]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: accountId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Account members retrieved successfully
 */
const getAccountMembers = asyncHandler(async (req, res) => {
  const accountId = req.params.accountId;

  const members = await AccountMember.findByAccountId(accountId);

  res.status(200).json({
    success: true,
    message: 'Account members retrieved successfully',
    data: { members }
  });
});

/**
 * @swagger
 * /api/account-members/user/{userId}:
 *   get:
 *     summary: Get user's account memberships
 *     tags: [Account Members]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User memberships retrieved successfully
 */
const getUserMemberships = asyncHandler(async (req, res) => {
  const userId = req.params.userId;

  const memberships = await AccountMember.findByUserId(userId);

  res.status(200).json({
    success: true,
    message: 'User memberships retrieved successfully',
    data: { memberships }
  });
});

/**
 * @swagger
 * /api/account-members:
 *   get:
 *     summary: Get all account members (admin only)
 *     tags: [Account Members]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: account_id
 *         schema:
 *           type: string
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Account members retrieved successfully
 */
const getAllAccountMembers = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const accountId = req.query.account_id;
  const userId = req.query.user_id;
  const skip = (page - 1) * limit;

  let query = {};

  if (accountId) {
    query.account_id = accountId;
  }

  if (userId) {
    query.user_id = userId;
  }

  // Get account members with pagination
  const [members, total] = await Promise.all([
    AccountMember.find(query)
      .populate('account_id', 'account_name account_id')
      .populate('user_id', 'email created_at')
      .populate('role_id', 'role_name')
      .populate('created_by', 'email')
      .populate('updated_by', 'email')
      .sort({ created_at: -1 })
      .skip(skip)
      .limit(limit),
    AccountMember.countDocuments(query)
  ]);

  const pagination = createPaginationMeta(page, limit, total);

  res.status(200).json({
    success: true,
    message: 'Account members retrieved successfully',
    data: { members, pagination }
  });
});

/**
 * @swagger
 * /api/account-members/{id}:
 *   put:
 *     summary: Update account member role (admin only)
 *     tags: [Account Members]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - role_id
 *             properties:
 *               role_id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Account member updated successfully
 */
const updateAccountMember = asyncHandler(async (req, res) => {
  const memberId = req.params.id;
  const { role_id } = req.body;

  const member = await AccountMember.findById(memberId);
  if (!member) {
    return res.status(404).json({
      success: false,
      message: 'Account member not found'
    });
  }

  // Verify role exists
  const role = await Role.findById(role_id);
  if (!role) {
    return res.status(404).json({
      success: false,
      message: 'Role not found'
    });
  }

  member.role_id = role_id;
  member.updated_by = req.user._id;
  await member.save();

  // Populate the updated member
  await member.populate([
    { path: 'user_id', select: 'email' },
    { path: 'role_id', select: 'role_name' },
    { path: 'account_id', select: 'account_name account_id' }
  ]);

  logger.info('Account member updated successfully', {
    memberId: member._id,
    accountId: member.account_id,
    userId: member.user_id,
    newRoleId: role_id,
    updatedBy: req.user.email
  });

  res.status(200).json({
    success: true,
    message: 'Account member updated successfully',
    data: { member }
  });
});

/**
 * @swagger
 * /api/account-members/{id}:
 *   delete:
 *     summary: Remove user from account (admin only)
 *     tags: [Account Members]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User removed from account successfully
 */
const removeAccountMember = asyncHandler(async (req, res) => {
  const memberId = req.params.id;

  const member = await AccountMember.findById(memberId);
  if (!member) {
    return res.status(404).json({
      success: false,
      message: 'Account member not found'
    });
  }

  await AccountMember.findByIdAndDelete(memberId);

  logger.info('User removed from account successfully', {
    memberId,
    accountId: member.account_id,
    userId: member.user_id,
    removedBy: req.user.email
  });

  res.status(200).json({
    success: true,
    message: 'User removed from account successfully'
  });
});

module.exports = {
  addAccountMember,
  getAccountMembers,
  getUserMemberships,
  getAllAccountMembers,
  updateAccountMember,
  removeAccountMember
};
