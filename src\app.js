const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const swaggerUi = require("swagger-ui-express");
const swaggerSpec = require("./config/swagger");
const connectDB = require("./config/database");
const connectRedis = require("./config/redis");
const logger = require("./utils/logger");
const errorHandler = require("./middleware/errorHandler");
const { initializeWebhookProcessor } = require("./jobs/webhookProcessor");

// Import routes
const authRoutes = require("./routes/auth");
const accountRoutes = require("./routes/accounts");
const destinationRoutes = require("./routes/destinations");
const userRoutes = require("./routes/users");
const accountMemberRoutes = require("./routes/accountMembers");
const logRoutes = require("./routes/logs");
const dataHandlerRoutes = require("./routes/dataHandler");

const app = express();

// Connect to databases
connectDB();
connectRedis();

// Initialize webhook processor
initializeWebhookProcessor();

// Security middleware
app.use(helmet());
app.use(cors());

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true }));

// Request logging
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path} - ${req.ip}`);
  next();
});

// API Documentation
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerSpec));

// Health check
app.get("/health", (req, res) => {
  res.status(200).json({
    success: true,
    message: "Data Pusher API is running",
    timestamp: new Date().toISOString(),
  });
});

// API Routes
app.use("/api/auth", authRoutes);
app.use("/api/accounts", accountRoutes);
app.use("/api/destinations", destinationRoutes);
app.use("/api/users", userRoutes);
app.use("/api/account-members", accountMemberRoutes);
app.use("/api/logs", logRoutes);
app.use("/server", dataHandlerRoutes);

// 404 handler
app.use("*", (req, res) => {
  res.status(404).json({
    success: false,
    message: "Route not found",
  });
});

// Error handling middleware
app.use(errorHandler);

module.exports = app;
