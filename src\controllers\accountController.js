const Account = require('../models/Account');
const AccountMember = require('../models/AccountMember');
const Role = require('../models/Role');
const cacheService = require('../services/cacheService');
const { asyncHandler } = require('../middleware/errorHandler');
const { createPaginationMeta } = require('../utils/helpers');
const { ROLES } = require('../utils/constants');
const logger = require('../utils/logger');

/**
 * @swagger
 * /api/accounts:
 *   post:
 *     summary: Create a new account
 *     tags: [Accounts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - account_name
 *             properties:
 *               account_name:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 100
 *               website:
 *                 type: string
 *                 format: uri
 *     responses:
 *       201:
 *         description: Account created successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 */
const createAccount = asyncHandler(async (req, res) => {
  const { account_name, website } = req.body;

  // Create account
  const account = await Account.create({
    account_name,
    website,
    created_by: req.user._id,
    updated_by: req.user._id
  });

  // Get admin role
  const adminRole = await Role.findByName(ROLES.ADMIN);
  if (!adminRole) {
    throw new Error('Admin role not found');
  }

  // Add creator as admin member
  await AccountMember.create({
    account_id: account._id,
    user_id: req.user._id,
    role_id: adminRole._id,
    created_by: req.user._id,
    updated_by: req.user._id
  });

  // Cache account
  await cacheService.cacheAccount(account._id.toString(), account.toJSON());

  logger.info('Account created successfully', {
    accountId: account._id,
    accountName: account.account_name,
    createdBy: req.user.email
  });

  res.status(201).json({
    success: true,
    message: 'Account created successfully',
    data: { account }
  });
});

/**
 * @swagger
 * /api/accounts:
 *   get:
 *     summary: Get all accounts (admin) or user's accounts
 *     tags: [Accounts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Accounts retrieved successfully
 */
const getAccounts = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const search = req.query.search || '';
  const skip = (page - 1) * limit;

  // Check if user is admin
  const isAdmin = await require('../services/authService').hasAdminRole(req.user._id);

  let query = {};
  let accountIds = [];

  if (!isAdmin) {
    // Get user's account memberships
    const memberships = await AccountMember.find({ user_id: req.user._id });
    accountIds = memberships.map(m => m.account_id);
    query._id = { $in: accountIds };
  }

  // Add search filter
  if (search) {
    query.account_name = { $regex: search, $options: 'i' };
  }

  // Get accounts with pagination
  const [accounts, total] = await Promise.all([
    Account.find(query)
      .populate('created_by', 'email')
      .populate('updated_by', 'email')
      .sort({ created_at: -1 })
      .skip(skip)
      .limit(limit),
    Account.countDocuments(query)
  ]);

  const pagination = createPaginationMeta(page, limit, total);

  res.status(200).json({
    success: true,
    message: 'Accounts retrieved successfully',
    data: { accounts, pagination }
  });
});

/**
 * @swagger
 * /api/accounts/{id}:
 *   get:
 *     summary: Get account by ID
 *     tags: [Accounts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Account retrieved successfully
 *       404:
 *         description: Account not found
 */
const getAccountById = asyncHandler(async (req, res) => {
  const accountId = req.params.id;

  // Try cache first
  let account = await cacheService.getCachedAccount(accountId);

  if (!account) {
    // Get from database
    account = await Account.findById(accountId)
      .populate('created_by', 'email')
      .populate('updated_by', 'email');

    if (!account) {
      return res.status(404).json({
        success: false,
        message: 'Account not found'
      });
    }

    // Cache the result
    await cacheService.cacheAccount(accountId, account.toJSON());
  }

  res.status(200).json({
    success: true,
    message: 'Account retrieved successfully',
    data: { account }
  });
});

/**
 * @swagger
 * /api/accounts/{id}:
 *   put:
 *     summary: Update account
 *     tags: [Accounts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               account_name:
 *                 type: string
 *               website:
 *                 type: string
 *     responses:
 *       200:
 *         description: Account updated successfully
 *       404:
 *         description: Account not found
 */
const updateAccount = asyncHandler(async (req, res) => {
  const accountId = req.params.id;
  const updateData = req.body;

  const account = await Account.findById(accountId);
  if (!account) {
    return res.status(404).json({
      success: false,
      message: 'Account not found'
    });
  }

  // Update fields
  Object.keys(updateData).forEach(key => {
    if (updateData[key] !== undefined) {
      account[key] = updateData[key];
    }
  });

  account.updated_by = req.user._id;
  await account.save();

  // Invalidate cache
  await cacheService.invalidateAccountCache(accountId);

  logger.info('Account updated successfully', {
    accountId: account._id,
    accountName: account.account_name,
    updatedBy: req.user.email
  });

  res.status(200).json({
    success: true,
    message: 'Account updated successfully',
    data: { account }
  });
});

/**
 * @swagger
 * /api/accounts/{id}:
 *   delete:
 *     summary: Delete account (admin only)
 *     tags: [Accounts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Account deleted successfully
 *       404:
 *         description: Account not found
 */
const deleteAccount = asyncHandler(async (req, res) => {
  const accountId = req.params.id;

  const account = await Account.findById(accountId);
  if (!account) {
    return res.status(404).json({
      success: false,
      message: 'Account not found'
    });
  }

  // Delete related data (cascade delete)
  await AccountMember.deleteMany({ account_id: accountId });
  
  // Delete account
  await Account.findByIdAndDelete(accountId);

  // Invalidate cache
  await cacheService.invalidateAccountCache(accountId);

  logger.info('Account deleted successfully', {
    accountId,
    accountName: account.account_name,
    deletedBy: req.user.email
  });

  res.status(200).json({
    success: true,
    message: 'Account deleted successfully'
  });
});

module.exports = {
  createAccount,
  getAccounts,
  getAccountById,
  updateAccount,
  deleteAccount
};
