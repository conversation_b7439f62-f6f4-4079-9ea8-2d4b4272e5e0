const express = require('express');
const {
  getUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser
} = require('../controllers/userController');
const { protect, requireAdmin } = require('../middleware/auth');
const { validate, userSchemas } = require('../middleware/validation');
const { generalApiRateLimit } = require('../middleware/rateLimiter');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Users
 *   description: User management endpoints
 */

// Apply general rate limiting
router.use(generalApiRateLimit);

// All routes require authentication
router.use(protect);

// Routes
router.route('/')
  .get(requireAdmin, getUsers)
  .post(requireAdmin, validate(userSchemas.register), createUser);

router.route('/:id')
  .get(getUserById)
  .put(validate(userSchemas.update), updateUser)
  .delete(requireAdmin, deleteUser);

module.exports = router;
