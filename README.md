# Data Pusher

A comprehensive Node.js Express web application that receives JSON data for accounts and forwards it to various destinations using webhook URLs.

## Features

### Core Modules
- **Account Management**: Create and manage accounts with unique IDs and secret tokens
- **Destination Management**: Configure webhook URLs with custom headers and HTTP methods
- **User Management**: User registration, authentication, and role-based access control
- **Account Members**: Manage user roles within accounts (Admin/Normal User)
- **Logging**: Comprehensive logging of all webhook deliveries with status tracking
- **Data Handler**: Core endpoint for receiving and processing JSON data

### Advanced Features
- **JWT Authentication**: Secure user authentication and authorization
- **Role-Based Access Control**: Admin and Normal User roles with different permissions
- **Asynchronous Processing**: Bull.js with Redis for background webhook processing
- **Rate Limiting**: Configurable rate limits for API endpoints and data handler
- **Caching**: Redis-based caching for improved performance
- **Data Validation**: Comprehensive input validation using Joi
- **Error Handling**: Centralized error handling with detailed logging
- **API Documentation**: Swagger/OpenAPI documentation
- **Testing**: Comprehensive test suite with Jest and Supertest

## Technology Stack

- **Runtime**: Node.js (Latest LTS)
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (JSON Web Tokens)
- **Queue System**: Bull.js with Redis
- **Caching**: Redis
- **Validation**: Joi
- **Testing**: Jest + Supertest
- **Documentation**: Swagger/OpenAPI
- **Logging**: Winston

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd data-pusher
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start MongoDB and Redis**
   ```bash
   # MongoDB (default: mongodb://localhost:27017)
   # Redis (default: localhost:6379)
   ```

5. **Seed the database**
   ```bash
   npm run seed
   ```

6. **Start the application**
   ```bash
   # Development
   npm run dev
   
   # Production
   npm start
   ```

## Environment Variables

```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/data-pusher

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Rate Limiting
RATE_LIMIT_WINDOW_MS=1000
RATE_LIMIT_MAX_REQUESTS=5

# Logging
LOG_LEVEL=info

# Admin User (for initial setup)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123
```

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user profile
- `POST /api/auth/logout` - User logout

### Accounts
- `GET /api/accounts` - Get accounts
- `POST /api/accounts` - Create account
- `GET /api/accounts/:id` - Get account by ID
- `PUT /api/accounts/:id` - Update account
- `DELETE /api/accounts/:id` - Delete account (Admin only)

### Destinations
- `GET /api/destinations` - Get destinations
- `POST /api/destinations` - Create destination
- `GET /api/destinations/account/:accountId` - Get destinations by account
- `GET /api/destinations/:id` - Get destination by ID
- `PUT /api/destinations/:id` - Update destination
- `DELETE /api/destinations/:id` - Delete destination

### Users (Admin only)
- `GET /api/users` - Get all users
- `POST /api/users` - Create user
- `GET /api/users/:id` - Get user by ID
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user

### Account Members
- `GET /api/account-members` - Get all memberships (Admin only)
- `POST /api/account-members` - Add user to account (Admin only)
- `GET /api/account-members/account/:accountId` - Get account members
- `GET /api/account-members/user/:userId` - Get user memberships
- `PUT /api/account-members/:id` - Update member role (Admin only)
- `DELETE /api/account-members/:id` - Remove member (Admin only)

### Logs
- `GET /api/logs` - Get logs (filtered by access)
- `GET /api/logs/account/:accountId` - Get logs by account
- `GET /api/logs/:id` - Get log by ID
- `GET /api/logs/event/:eventId` - Get logs by event ID
- `POST /api/logs/:id/retry` - Retry failed webhook (Admin only)
- `GET /api/logs/stats/:accountId` - Get webhook statistics

### Data Handler
- `POST /server/incoming_data` - Receive and process JSON data
- `GET /server/health` - Health check

## Data Handler Usage

The core functionality is the `/server/incoming_data` endpoint:

### Headers Required
- `CL-X-TOKEN`: Account secret token (required)
- `CL-X-EVENT-ID`: Event ID (optional, auto-generated if not provided)

### Example Request
```bash
curl -X POST http://localhost:3000/server/incoming_data \
  -H "Content-Type: application/json" \
  -H "CL-X-TOKEN: your-account-secret-token" \
  -H "CL-X-EVENT-ID: unique-event-id" \
  -d '{
    "user_id": 123,
    "action": "user.update",
    "data": {
      "name": "John Doe",
      "email": "<EMAIL>"
    }
  }'
```

### Response
```json
{
  "success": true,
  "message": "Data Received",
  "data": {
    "event_id": "unique-event-id",
    "destinations_count": 2
  }
}
```

## Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## API Documentation

Once the server is running, visit:
- **Swagger UI**: http://localhost:3000/api-docs

## Rate Limiting

- **Data Handler**: 5 requests per second per account
- **General API**: 100 requests per 15 minutes per user
- **Authentication**: 10 requests per 15 minutes per IP

## Logging

Logs are written to:
- `logs/error.log` - Error logs only
- `logs/combined.log` - All logs
- Console output in development mode

## Queue Processing

The application uses Bull.js with Redis for asynchronous webhook processing:
- Failed jobs are automatically retried (3 attempts)
- Exponential backoff for retries
- Job statistics and monitoring available

## Security Features

- JWT-based authentication
- Password hashing with bcrypt
- Rate limiting
- Input validation and sanitization
- CORS protection
- Helmet security headers
- SQL injection prevention (NoSQL)

## Performance Features

- Redis caching for frequently accessed data
- Database indexing for optimized queries
- Connection pooling
- Async processing for webhooks
- Pagination for large datasets

## Development

```bash
# Start in development mode with auto-reload
npm run dev

# Seed database with roles and admin user
npm run seed

# Run linting (if configured)
npm run lint

# Run tests
npm test
```

## Production Deployment

1. Set `NODE_ENV=production`
2. Use a process manager like PM2
3. Set up MongoDB replica set
4. Configure Redis cluster
5. Use a reverse proxy (nginx)
6. Set up SSL/TLS certificates
7. Configure monitoring and logging

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

MIT License - see LICENSE file for details
