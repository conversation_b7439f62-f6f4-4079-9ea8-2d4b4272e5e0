const express = require('express');
const {
  createAccount,
  getAccounts,
  getAccountById,
  updateAccount,
  deleteAccount
} = require('../controllers/accountController');
const { protect, requireAdmin, requireAccountAccess } = require('../middleware/auth');
const { validate, accountSchemas } = require('../middleware/validation');
const { generalApiRateLimit } = require('../middleware/rateLimiter');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Accounts
 *   description: Account management endpoints
 */

// Apply general rate limiting
router.use(generalApiRateLimit);

// All routes require authentication
router.use(protect);

// Routes
router.route('/')
  .get(getAccounts)
  .post(validate(accountSchemas.create), createAccount);

router.route('/:id')
  .get(requireAccountAccess(), getAccountById)
  .put(requireAccountAccess(), validate(accountSchemas.update), updateAccount)
  .delete(requireAdmin, deleteAccount);

module.exports = router;
