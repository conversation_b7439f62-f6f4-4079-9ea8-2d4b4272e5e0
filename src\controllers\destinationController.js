const Destination = require('../models/Destination');
const Account = require('../models/Account');
const cacheService = require('../services/cacheService');
const { asyncHandler } = require('../middleware/errorHandler');
const { createPaginationMeta } = require('../utils/helpers');
const logger = require('../utils/logger');

/**
 * @swagger
 * /api/destinations:
 *   post:
 *     summary: Create a new destination
 *     tags: [Destinations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - account_id
 *               - url
 *               - http_method
 *               - headers
 *             properties:
 *               account_id:
 *                 type: string
 *               url:
 *                 type: string
 *                 format: uri
 *               http_method:
 *                 type: string
 *                 enum: [GET, POST, PUT, PATCH, DELETE]
 *               headers:
 *                 type: object
 *     responses:
 *       201:
 *         description: Destination created successfully
 */
const createDestination = asyncHandler(async (req, res) => {
  const { account_id, url, http_method, headers } = req.body;

  // Verify account exists and user has access
  const account = await Account.findById(account_id);
  if (!account) {
    return res.status(404).json({
      success: false,
      message: 'Account not found'
    });
  }

  // Create destination
  const destination = await Destination.create({
    account_id,
    url,
    http_method: http_method || 'POST',
    headers: new Map(Object.entries(headers)),
    created_by: req.user._id,
    updated_by: req.user._id
  });

  // Invalidate destinations cache for this account
  await cacheService.del(`${require('../utils/constants').CACHE_KEYS.DESTINATION_PREFIX}${account_id}`);

  logger.info('Destination created successfully', {
    destinationId: destination._id,
    accountId: account_id,
    url: destination.url,
    createdBy: req.user.email
  });

  res.status(201).json({
    success: true,
    message: 'Destination created successfully',
    data: { destination }
  });
});

/**
 * @swagger
 * /api/destinations:
 *   get:
 *     summary: Get destinations (filtered by account access)
 *     tags: [Destinations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: account_id
 *         schema:
 *           type: string
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *     responses:
 *       200:
 *         description: Destinations retrieved successfully
 */
const getDestinations = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const accountId = req.query.account_id;
  const skip = (page - 1) * limit;

  let query = {};

  // If account_id is specified, filter by it
  if (accountId) {
    query.account_id = accountId;
  } else {
    // Get user's accessible accounts
    const AccountMember = require('../models/AccountMember');
    const memberships = await AccountMember.find({ user_id: req.user._id });
    const accountIds = memberships.map(m => m.account_id);
    query.account_id = { $in: accountIds };
  }

  // Get destinations with pagination
  const [destinations, total] = await Promise.all([
    Destination.find(query)
      .populate('account_id', 'account_name account_id')
      .populate('created_by', 'email')
      .populate('updated_by', 'email')
      .sort({ created_at: -1 })
      .skip(skip)
      .limit(limit),
    Destination.countDocuments(query)
  ]);

  const pagination = createPaginationMeta(page, limit, total);

  res.status(200).json({
    success: true,
    message: 'Destinations retrieved successfully',
    data: { destinations, pagination }
  });
});

/**
 * @swagger
 * /api/destinations/account/{accountId}:
 *   get:
 *     summary: Get destinations by account ID
 *     tags: [Destinations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: accountId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Destinations retrieved successfully
 */
const getDestinationsByAccount = asyncHandler(async (req, res) => {
  const accountId = req.params.accountId;

  // Try cache first
  let destinations = await cacheService.getCachedDestinations(accountId);

  if (!destinations) {
    // Get from database
    destinations = await Destination.findByAccountId(accountId)
      .populate('created_by', 'email')
      .populate('updated_by', 'email');

    // Cache the result
    await cacheService.cacheDestinations(accountId, destinations);
  }

  res.status(200).json({
    success: true,
    message: 'Destinations retrieved successfully',
    data: { destinations }
  });
});

/**
 * @swagger
 * /api/destinations/{id}:
 *   get:
 *     summary: Get destination by ID
 *     tags: [Destinations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Destination retrieved successfully
 */
const getDestinationById = asyncHandler(async (req, res) => {
  const destinationId = req.params.id;

  const destination = await Destination.findById(destinationId)
    .populate('account_id', 'account_name account_id')
    .populate('created_by', 'email')
    .populate('updated_by', 'email');

  if (!destination) {
    return res.status(404).json({
      success: false,
      message: 'Destination not found'
    });
  }

  res.status(200).json({
    success: true,
    message: 'Destination retrieved successfully',
    data: { destination }
  });
});

/**
 * @swagger
 * /api/destinations/{id}:
 *   put:
 *     summary: Update destination
 *     tags: [Destinations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Destination updated successfully
 */
const updateDestination = asyncHandler(async (req, res) => {
  const destinationId = req.params.id;
  const updateData = req.body;

  const destination = await Destination.findById(destinationId);
  if (!destination) {
    return res.status(404).json({
      success: false,
      message: 'Destination not found'
    });
  }

  // Update fields
  Object.keys(updateData).forEach(key => {
    if (updateData[key] !== undefined) {
      if (key === 'headers') {
        destination[key] = new Map(Object.entries(updateData[key]));
      } else {
        destination[key] = updateData[key];
      }
    }
  });

  destination.updated_by = req.user._id;
  await destination.save();

  // Invalidate cache
  await cacheService.del(`${require('../utils/constants').CACHE_KEYS.DESTINATION_PREFIX}${destination.account_id}`);

  logger.info('Destination updated successfully', {
    destinationId: destination._id,
    accountId: destination.account_id,
    updatedBy: req.user.email
  });

  res.status(200).json({
    success: true,
    message: 'Destination updated successfully',
    data: { destination }
  });
});

/**
 * @swagger
 * /api/destinations/{id}:
 *   delete:
 *     summary: Delete destination
 *     tags: [Destinations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Destination deleted successfully
 */
const deleteDestination = asyncHandler(async (req, res) => {
  const destinationId = req.params.id;

  const destination = await Destination.findById(destinationId);
  if (!destination) {
    return res.status(404).json({
      success: false,
      message: 'Destination not found'
    });
  }

  await Destination.findByIdAndDelete(destinationId);

  // Invalidate cache
  await cacheService.del(`${require('../utils/constants').CACHE_KEYS.DESTINATION_PREFIX}${destination.account_id}`);

  logger.info('Destination deleted successfully', {
    destinationId,
    accountId: destination.account_id,
    deletedBy: req.user.email
  });

  res.status(200).json({
    success: true,
    message: 'Destination deleted successfully'
  });
});

module.exports = {
  createDestination,
  getDestinations,
  getDestinationsByAccount,
  getDestinationById,
  updateDestination,
  deleteDestination
};
